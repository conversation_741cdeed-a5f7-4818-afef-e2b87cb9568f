version: '3.8'

services:
  nxt-platform:
    image: ghcr.io/nxtleveltech1/dev-xxx:latest
    container_name: nxt-platform-prod
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      # Database Configuration
      - DATABASE_URL=${DATABASE_URL}
      # Better Auth Configuration
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - BETTER_AUTH_URL=https://nxtdotx.co.za
      # Security Configuration
      - CORS_ORIGIN=https://nxtdotx.co.za
      - TRUSTED_ORIGINS=https://nxtdotx.co.za,https://www.nxtdotx.co.za
    volumes:
      # Optional: Mount logs directory
      - ./logs:/app/logs
    networks:
      - nxt-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.nxt-platform.rule=Host(`nxtdotx.co.za`)"
      - "traefik.http.routers.nxt-platform.tls=true"
      - "traefik.http.routers.nxt-platform.tls.certresolver=letsencrypt"

  # Optional: Reverse Proxy with SSL
  traefik:
    image: traefik:v3.0
    container_name: nxt-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    command:
      - --api.dashboard=true
      - --api.insecure=true
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      - --certificatesresolvers.letsencrypt.acme.tlschallenge=true
      - --certificatesresolvers.letsencrypt.acme.email=<EMAIL>
      - --certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json
      - --entrypoints.web.http.redirections.entrypoint.to=websecure
      - --entrypoints.web.http.redirections.entrypoint.scheme=https
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./letsencrypt:/letsencrypt
    networks:
      - nxt-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.traefik.rule=Host(`traefik.nxtdotx.co.za`)"
      - "traefik.http.routers.traefik.tls=true"
      - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"

networks:
  nxt-network:
    driver: bridge

volumes:
  letsencrypt:
    driver: local
