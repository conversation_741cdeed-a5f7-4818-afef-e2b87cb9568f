# ------------------------------------------------------------------------------
# Docker Image for NXT-DOT-X Platform
# Intended for deployment to GitHub Container Registry (ghcr.io)
# Repository: ghcr.io/nxtleveltech1/dev-xxx:latest (lowercase naming convention)
# ------------------------------------------------------------------------------
# 1) Build Stage (Client)
# ------------------------------------------------------------------------------
FROM node:20-alpine AS build

WORKDIR /app

# Copy only package.json / package-lock.json for dependencies
COPY package*.json ./

# Install dependencies (Node 20+ uses corepack for Yarn/Pnpm, but we keep npm for consistency)
RUN npm ci --legacy-peer-deps

# Now copy only the client-related files to build the frontend
# Exclude any server folder or node-based code from the build context
# If your approach has a dedicated "public/" or "client/" or "src/” with only client code, adjust accordingly
COPY vite.config.ts .
COPY tailwind.config.js .
COPY tsconfig.json .
COPY tsconfig.tests.json .
COPY postcss.config.js .

# Copy everything from src EXCEPT the server folder
COPY src ./src
COPY public ./public

COPY index.html ./
# Build the client
RUN set -o pipefail && npm run build 2>&1 | tee build.log

# ------------------------------------------------------------------------------
# 2) Production Stage (Server)
# ------------------------------------------------------------------------------
FROM node:20-alpine

# Install curl for health checks
RUN apk add --no-cache curl

WORKDIR /app
ENV NODE_ENV=production
ENV HOST=0.0.0.0
# Internal application port
ENV PORT=3000

# Copy over the built client code
COPY --from=build /app/dist ./dist
COPY --from=build /app/build.log ./build.log

# Copy monitoring scripts or other universal resources if needed
COPY package*.json ./

# Now copy in the server folder, which must remain separate from the client build
# This ensures the server code is never processed by Vite, preventing "postgres" errors in the browser
COPY server ./server
COPY src/lib ./src/lib

# If you also have other server-level config files, copy them here
# e.g.: COPY supabase ./supabase
# or COPY .env ./

# Install only production dependencies
RUN npm ci --only=production --legacy-peer-deps

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001
USER nextjs

# If your production server entry point is "server/production-better-auth-server.js", run it
CMD ["node", "server/production-better-auth-server.mjs"]
