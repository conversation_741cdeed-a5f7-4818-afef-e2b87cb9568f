import { createAuthClient } from 'better-auth/react';

// FORCE LOCAL DEVELOPMENT - BYPASS AUTH FOR RAPID DEVELOPMENT
console.log('🚀 LOCAL DEV MODE - BYPASSING AUTH FOR RAPID DEVELOPMENT');
console.log('🔍 Window location:', window.location.href);

// For local development, we'll fake the auth
const isLocalDev = window.location.hostname === 'localhost';
const authURL = isLocalDev ? 'http://localhost:3002/api/auth' : 'https://nxtdotx.co.za/api/auth';
console.log('🔍 Auth URL:', authURL);
console.log('🔍 Local Dev Mode:', isLocalDev);

export const authClient = createAuthClient({
  baseURL: authURL,
  fetchOptions: {
    credentials: 'include',
  },
  // Force HTTP for local development - override internal URL handling
  $fetch: async (url, options = {}) => {
    // Force HTTP protocol for localhost
    const fixedUrl = url.toString().replace('https://localhost:', 'http://localhost:');
    console.log('🔍 Original URL:', url);
    console.log('🔍 Fixed URL:', fixedUrl);

    const response = await fetch(fixedUrl, {
      ...options,
      credentials: 'include',
    });
    console.log('🔍 Response status:', response.status);
    return response;
  }
});