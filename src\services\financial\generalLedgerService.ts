import { supabase } from '@/integrations/supabase';

export interface GeneralLedgerEntry {
  id: string;
  transaction_id: string;
  account_id: string;
  account_code: string;
  account_name: string;
  account_type: string;
  transaction_date: string;
  description: string;
  debit_amount: number;
  credit_amount: number;
  reference_type?: string;
  reference_id?: string;
  running_balance: number;
  created_at: string;
}

export interface TrialBalanceEntry {
  account_code: string;
  account_name: string;
  account_type: string;
  debit_balance: number;
  credit_balance: number;
}

export interface AccountBalance {
  account_id: string;
  account_code: string;
  account_name: string;
  account_type: string;
  balance: number;
}

export class GeneralLedgerService {
  /**
   * Get general ledger entries with optional filters
   */
  static async getLedgerEntries(
    organizationId: string = 'default-org-id',
    filters: {
      accountId?: string;
      startDate?: string;
      endDate?: string;
      limit?: number;
    } = {}
  ): Promise<GeneralLedgerEntry[]> {
    try {
      let query = supabase
        .from('general_ledger')
        .select(`
          *,
          chart_of_accounts!inner (
            account_code,
            account_name,
            account_type
          )
        `)
        .eq('organization_id', organizationId)
        .order('transaction_date', { ascending: false })
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.accountId && filters.accountId !== 'ALL') {
        query = query.eq('account_id', filters.accountId);
      }
      if (filters.startDate) {
        query = query.gte('transaction_date', filters.startDate);
      }
      if (filters.endDate) {
        query = query.lte('transaction_date', filters.endDate);
      }
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch ledger entries: ${error.message}`);
      }

      // Transform data to match our interface
      const transformedEntries: GeneralLedgerEntry[] = (data || []).map((entry: any) => ({
        id: entry.id,
        transaction_id: entry.transaction_id,
        account_id: entry.account_id,
        account_code: entry.chart_of_accounts.account_code,
        account_name: entry.chart_of_accounts.account_name,
        account_type: entry.chart_of_accounts.account_type,
        transaction_date: entry.transaction_date,
        description: entry.description,
        debit_amount: entry.debit_amount || 0,
        credit_amount: entry.credit_amount || 0,
        reference_type: entry.reference_type,
        reference_id: entry.reference_id,
        running_balance: 0, // Will be calculated
        created_at: entry.created_at
      }));

      // Calculate running balances
      return this.calculateRunningBalances(transformedEntries);
    } catch (error) {
      console.error('GeneralLedgerService.getLedgerEntries error:', error);
      throw error;
    }
  }

  /**
   * Get trial balance using database function or manual calculation
   */
  static async getTrialBalance(
    organizationId: string = 'default-org-id',
    asOfDate?: string
  ): Promise<TrialBalanceEntry[]> {
    try {
      const targetDate = asOfDate || new Date().toISOString().split('T')[0];
      
      // Try to use the database function first
      const { data, error } = await supabase.rpc('get_trial_balance', {
        p_organization_id: organizationId,
        p_as_of_date: targetDate
      });

      if (error) {
        console.warn('Database function not available, calculating manually:', error);
        return await this.calculateTrialBalanceManually(organizationId, targetDate);
      }

      return data || [];
    } catch (error) {
      console.error('GeneralLedgerService.getTrialBalance error:', error);
      // Fallback to manual calculation
      return await this.calculateTrialBalanceManually(organizationId, asOfDate);
    }
  }

  /**
   * Get account balances
   */
  static async getAccountBalances(
    organizationId: string = 'default-org-id',
    asOfDate?: string
  ): Promise<AccountBalance[]> {
    try {
      const { data, error } = await supabase
        .from('chart_of_accounts')
        .select('id, account_code, account_name, account_type')
        .eq('organization_id', organizationId)
        .eq('is_active', true)
        .order('account_code');

      if (error) throw error;

      const balances: AccountBalance[] = [];
      
      for (const account of data || []) {
        try {
          // Try to use the database function
          const { data: balanceData, error: balanceError } = await supabase.rpc('get_account_balance', {
            p_account_id: account.id,
            p_as_of_date: asOfDate || new Date().toISOString().split('T')[0]
          });

          const balance = balanceError ? 0 : (balanceData || 0);
          
          balances.push({
            account_id: account.id,
            account_code: account.account_code,
            account_name: account.account_name,
            account_type: account.account_type,
            balance: balance
          });
        } catch (error) {
          console.warn(`Error getting balance for account ${account.account_code}:`, error);
          balances.push({
            account_id: account.id,
            account_code: account.account_code,
            account_name: account.account_name,
            account_type: account.account_type,
            balance: 0
          });
        }
      }

      return balances;
    } catch (error) {
      console.error('GeneralLedgerService.getAccountBalances error:', error);
      throw error;
    }
  }

  /**
   * Manually calculate trial balance if DB function is not available
   */
  private static async calculateTrialBalanceManually(
    organizationId: string,
    asOfDate?: string
  ): Promise<TrialBalanceEntry[]> {
    const entries = await this.getLedgerEntries(organizationId, { endDate: asOfDate });
    const trialBalance = new Map<string, TrialBalanceEntry>();

    for (const entry of entries) {
      if (!trialBalance.has(entry.account_code)) {
        trialBalance.set(entry.account_code, {
          account_code: entry.account_code,
          account_name: entry.account_name,
          account_type: entry.account_type,
          debit_balance: 0,
          credit_balance: 0,
        });
      }

      const tbEntry = trialBalance.get(entry.account_code)!;
      tbEntry.debit_balance += entry.debit_amount;
      tbEntry.credit_balance += entry.credit_amount;
    }

    return Array.from(trialBalance.values());
  }

  /**
   * Calculate running balances for a set of entries
   */
  private static calculateRunningBalances(entries: GeneralLedgerEntry[]): GeneralLedgerEntry[] {
    const accountGroups = new Map<string, GeneralLedgerEntry[]>();
    
    // Group entries by account
    entries.forEach(entry => {
      if (!accountGroups.has(entry.account_id)) {
        accountGroups.set(entry.account_id, []);
      }
      accountGroups.get(entry.account_id)!.push(entry);
    });

    const result: GeneralLedgerEntry[] = [];

    // Calculate running balance for each account
    accountGroups.forEach((accountEntries, accountId) => {
      const sortedEntries = accountEntries.sort((a, b) => 
        new Date(a.transaction_date).getTime() - new Date(b.transaction_date).getTime() ||
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      );
      
      let currentBalance = 0;
      sortedEntries.forEach(entry => {
        currentBalance += entry.debit_amount - entry.credit_amount;
        entry.running_balance = currentBalance;
        result.push(entry);
      });
    });

    // Sort final result by date
    return result.sort((a, b) => 
      new Date(b.transaction_date).getTime() - new Date(a.transaction_date).getTime() ||
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  }
  
  /**
   * Fetches financial summary from a Supabase view.
   * @param organizationId The ID of the organization.
   * @returns A promise that resolves to the financial summary.
   */
  static async getFinancialSummary(organizationId: string): Promise<any> {
    const { data, error } = await supabase
      .from('financial_summary_view')
      .select('*')
      .eq('organization_id', organizationId)
      .single();

    if (error) {
      console.error('Error fetching financial summary:', error);
      throw new Error('Could not fetch financial summary.');
    }
    return data;
  }

  /**
   * Fetches detailed financial data from multiple related tables.
   * @param organizationId The ID of the organization.
   * @returns A promise that resolves to the detailed financial data.
   */
  static async getDetailedFinancials(organizationId: string): Promise<any[]> {
    const { data, error } = await supabase
      .from('chart_of_accounts')
      .select(`
        *,
        general_ledger!inner (
          *
        ),
        financial_statements!inner (
          *
        )
      `)
      .eq('organization_id', organizationId);

    if (error) {
      console.error('Error fetching detailed financials:', error);
      throw new Error('Could not fetch detailed financials.');
    }
    
    const result: any[] = [];
    if (data) {
      (data || []).forEach((account: any) => {
        const accountData = { ...account };
        delete accountData.general_ledger;
        delete accountData.financial_statements;

        account.general_ledger.forEach((gl: any) => {
          const statement = account.financial_statements.find((fs: any) => fs.id === gl.statement_id);
          result.push({
            ...accountData,
            ...gl,
            statement_name: statement ? statement.name : null
          });
        });
      });
    }
    return result;
  }
}
