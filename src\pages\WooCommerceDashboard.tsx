import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { WooCommerceConfig } from '@/components/integrations/WooCommerceConfig';
import { 
  ShoppingCart, 
  Users, 
  Package, 
  DollarSign, 
  TrendingUp, 
  Settings,
  RefreshCw,
  ExternalLink
} from 'lucide-react';

interface WooCommerceStats {
  customers: number;
  products: number;
  orders: number;
  revenue: string;
}

export function WooCommerceDashboard() {
  const [stats, setStats] = useState<WooCommerceStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchStats = async () => {
    setIsLoading(true);
    try {
      // Simulate API calls - replace with actual WooCommerce API calls
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStats({
        customers: 1247,
        products: 89,
        orders: 342,
        revenue: 'R 45,230.50'
      });
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  const StatCard = ({ title, value, icon: Icon, trend }: any) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {trend && (
          <p className="text-xs text-muted-foreground">
            <span className="text-green-600">+{trend}%</span> from last month
          </p>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">WooCommerce Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your WooCommerce store integration
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchStats}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open('https://nxtleveltech.co.za/wp-admin', '_blank')}
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            WP Admin
          </Button>
        </div>
      </div>

      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Connection Status
            <Badge variant="default" className="bg-green-500">Connected</Badge>
          </CardTitle>
          <CardDescription>
            Store: https://nxtleveltech.co.za/ | API Version: v3
            {lastUpdated && (
              <span className="block text-xs mt-1">
                Last updated: {lastUpdated.toLocaleString()}
              </span>
            )}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Stats Overview */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Customers"
            value={stats.customers.toLocaleString()}
            icon={Users}
            trend={12}
          />
          <StatCard
            title="Products"
            value={stats.products}
            icon={Package}
            trend={5}
          />
          <StatCard
            title="Orders"
            value={stats.orders.toLocaleString()}
            icon={ShoppingCart}
            trend={8}
          />
          <StatCard
            title="Revenue"
            value={stats.revenue}
            icon={DollarSign}
            trend={15}
          />
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Recent Orders</CardTitle>
                <CardDescription>Latest orders from your store</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <p className="font-medium">Order #1234</p>
                      <p className="text-sm text-muted-foreground">John Doe</p>
                    </div>
                    <Badge>R 299.99</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <p className="font-medium">Order #1233</p>
                      <p className="text-sm text-muted-foreground">Jane Smith</p>
                    </div>
                    <Badge>R 149.50</Badge>
                  </div>
                  <div className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <p className="font-medium">Order #1232</p>
                      <p className="text-sm text-muted-foreground">Mike Johnson</p>
                    </div>
                    <Badge>R 89.99</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Products</CardTitle>
                <CardDescription>Best selling products this month</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <p className="font-medium">Premium Widget</p>
                      <p className="text-sm text-muted-foreground">45 sold</p>
                    </div>
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <p className="font-medium">Basic Package</p>
                      <p className="text-sm text-muted-foreground">32 sold</p>
                    </div>
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  </div>
                  <div className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <p className="font-medium">Starter Kit</p>
                      <p className="text-sm text-muted-foreground">28 sold</p>
                    </div>
                    <TrendingUp className="h-4 w-4 text-green-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Product Management</CardTitle>
              <CardDescription>Manage your WooCommerce products</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Product management interface coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Order Management</CardTitle>
              <CardDescription>View and manage orders</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Order management interface coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Management</CardTitle>
              <CardDescription>Manage customer data</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Customer management interface coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <WooCommerceConfig />
        </TabsContent>
      </Tabs>
    </div>
  );
}
