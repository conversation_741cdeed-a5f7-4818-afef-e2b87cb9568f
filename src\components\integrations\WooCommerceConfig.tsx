import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2, Eye, EyeOff, Download, Database } from 'lucide-react';

interface WooCommerceConfig {
  storeUrl: string;
  consumerKey: string;
  consumerSecret: string;
}

interface StagingData {
  customers: any[];
  products: any[];
  orders: any[];
  lastSync: Date;
}

export function WooCommerceConfig() {
  const [config, setConfig] = useState<WooCommerceConfig>({
    storeUrl: 'https://nxtleveltech.co.za/',
    consumerKey: 'ck_ad4701db790c558f2318f8c0dc3170854edb764b',
    consumerSecret: ''
  });

  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'connected' | 'error'>('idle');
  const [stagingData, setStagingData] = useState<StagingData | null>(null);
  const [showSecret, setShowSecret] = useState(false);
  const [currentStep, setCurrentStep] = useState<'connect' | 'download' | 'review'>('connect');

  const handleInputChange = (field: keyof WooCommerceConfig, value: string) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const testConnection = async () => {
    setIsLoading(true);
    setTestResults(null);

    try {
      // REAL WooCommerce API call
      const auth = btoa(`${config.consumerKey}:${config.consumerSecret}`);
      const url = `${config.storeUrl.replace(/\/$/, '')}/wp-json/wc/${config.apiVersion}/system_status`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const result = await response.json();
        setConnectionStatus('success');
        setTestResults({
          success: true,
          message: 'Connected successfully! WooCommerce API is accessible.',
          data: result
        });
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      setConnectionStatus('error');
      setTestResults({ error: 'Connection failed: ' + (error as Error).message });
    } finally {
      setIsLoading(false);
    }
  };

  const testCustomersAPI = async () => {
    setIsLoading(true);
    try {
      // REAL WooCommerce Customers API call
      const auth = btoa(`${config.consumerKey}:${config.consumerSecret}`);
      const url = `${config.storeUrl.replace(/\/$/, '')}/wp-json/wc/${config.apiVersion}/customers?per_page=5`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const customers = await response.json();
        setTestResults({
          customers: {
            success: true,
            count: customers.length,
            data: customers
          }
        });
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      setTestResults({ error: 'Customers API test failed: ' + (error as Error).message });
    } finally {
      setIsLoading(false);
    }
  };

  const testProductsAPI = async () => {
    setIsLoading(true);
    try {
      // REAL WooCommerce Products API call
      const auth = btoa(`${config.consumerKey}:${config.consumerSecret}`);
      const url = `${config.storeUrl.replace(/\/$/, '')}/wp-json/wc/${config.apiVersion}/products?per_page=5`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const products = await response.json();
        setTestResults({
          products: {
            success: true,
            count: products.length,
            data: products
          }
        });
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      setTestResults({ error: 'Products API test failed: ' + (error as Error).message });
    } finally {
      setIsLoading(false);
    }
  };

  const saveConfiguration = async () => {
    setIsLoading(true);
    try {
      // Save to localStorage (no backend needed for config)
      localStorage.setItem('woocommerce_config', JSON.stringify(config));

      setConnectionStatus('success');
      alert('✅ Configuration saved successfully!');
    } catch (error) {
      setConnectionStatus('error');
      alert('❌ Save failed: ' + (error as Error).message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            WooCommerce API Configuration
            {connectionStatus === 'success' && <Badge variant="default" className="bg-green-500">Connected</Badge>}
            {connectionStatus === 'error' && <Badge variant="destructive">Error</Badge>}
          </CardTitle>
          <CardDescription>
            Configure your WooCommerce store API connection
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="storeUrl">Store URL</Label>
              <Input
                id="storeUrl"
                value={config.storeUrl}
                onChange={(e) => handleInputChange('storeUrl', e.target.value)}
                placeholder="https://your-store.com/"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="apiVersion">API Version</Label>
              <select
                id="apiVersion"
                value={config.apiVersion}
                onChange={(e) => handleInputChange('apiVersion', e.target.value)}
                className="w-full p-2 border rounded-md"
              >
                <option value="v3">v3 (Recommended)</option>
                <option value="v2">v2</option>
                <option value="v1">v1</option>
              </select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="consumerKey">Consumer Key</Label>
            <Input
              id="consumerKey"
              value={config.consumerKey}
              onChange={(e) => handleInputChange('consumerKey', e.target.value)}
              placeholder="ck_..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="consumerSecret">Consumer Secret</Label>
            <div className="relative">
              <Input
                id="consumerSecret"
                type={showSecret ? 'text' : 'password'}
                value={config.consumerSecret}
                onChange={(e) => handleInputChange('consumerSecret', e.target.value)}
                placeholder="cs_..."
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 -translate-y-1/2"
                onClick={() => setShowSecret(!showSecret)}
              >
                {showSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button onClick={testConnection} disabled={isLoading}>
              {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Test Connection
            </Button>
            <Button onClick={testCustomersAPI} variant="outline" disabled={isLoading}>
              Test Customers API
            </Button>
            <Button onClick={testProductsAPI} variant="outline" disabled={isLoading}>
              Test Products API
            </Button>
            <Button onClick={saveConfiguration} variant="default" disabled={isLoading}>
              Save Configuration
            </Button>
          </div>

          {connectionStatus === 'success' && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                ✅ Connected successfully! WooCommerce API is accessible.
              </AlertDescription>
            </Alert>
          )}

          {connectionStatus === 'error' && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                ❌ Connection failed. Please check your credentials.
              </AlertDescription>
            </Alert>
          )}

          {testResults && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">API Response</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
                  {JSON.stringify(testResults, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
