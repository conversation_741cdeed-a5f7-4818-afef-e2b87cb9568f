#!/usr/bin/env pwsh
# LOCAL DEVELOPMENT SCRIPT - INSTANT STARTUP

Write-Host "🏠 STARTING LOCAL DEVELOPMENT ENVIRONMENT" -ForegroundColor Green
Write-Host "⚡ Ultra-fast local setup for 2-hour deadline..." -ForegroundColor Yellow

# Step 1: Environment Setup
Write-Host "🔧 Setting up environment..." -ForegroundColor Cyan
if (!(Test-Path ".env.local")) {
    Copy-Item ".env.production" ".env.local"
    Write-Host "📝 Created .env.local from production template" -ForegroundColor Green
}

# Step 2: Install Dependencies (if needed)
if (!(Test-Path "node_modules")) {
    Write-Host "📦 Installing dependencies..." -ForegroundColor Cyan
    npm install --silent
}

# Step 3: Start Development Servers
Write-Host "🚀 Starting development servers..." -ForegroundColor Cyan

# Start backend server
Write-Host "🔧 Starting Better Auth server on port 3000..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command", "cd '$PWD'; $env:NODE_ENV='development'; node server/production-better-auth-server.mjs" -WindowStyle Minimized

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Start frontend dev server
Write-Host "⚡ Starting Vite dev server on port 5173..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-Command", "cd '$PWD'; npm run dev" -WindowStyle Minimized

# Wait for servers to start
Start-Sleep -Seconds 5

Write-Host "✅ LOCAL DEVELOPMENT READY!" -ForegroundColor Green
Write-Host "🌐 Frontend: http://localhost:5173" -ForegroundColor Cyan
Write-Host "🔐 Backend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "🔍 Health: http://localhost:3000/health" -ForegroundColor Cyan

# Open browser
Write-Host "🌍 Opening browser..." -ForegroundColor Yellow
Start-Process "http://localhost:5173"

Write-Host "🎯 READY FOR RAPID DEVELOPMENT!" -ForegroundColor Green
Write-Host "💡 Use rapid-dev.ps1 to deploy changes in 2 minutes" -ForegroundColor Yellow
