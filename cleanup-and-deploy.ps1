# Complete Docker Cleanup and Fresh Deployment Script (PowerShell)
# This will remove ALL containers, images, and networks related to the project

Write-Host "🧹 COMPLETE DOCKER CLEANUP AND FRESH DEPLOYMENT" -ForegroundColor Green
Write-Host "================================================" -ForegroundColor Green

# Configuration
$CONTAINER_IMAGE = "ghcr.io/nxtleveltech1/dev-xxx:latest"
$COMPOSE_FILE = "docker-compose.coolify.yml"

Write-Host "⚠️  WARNING: This will delete ALL Docker containers and images!" -ForegroundColor Red
Write-Host "Press Enter to continue or Ctrl+C to cancel..." -ForegroundColor Yellow
Read-Host

# Step 1: Stop and remove ALL containers
Write-Host "🛑 Stopping all running containers..." -ForegroundColor Blue
try {
    $runningContainers = docker ps -aq
    if ($runningContainers) {
        docker stop $runningContainers
        Write-Host "Stopped containers" -ForegroundColor Green
    } else {
        Write-Host "No running containers to stop" -ForegroundColor Yellow
    }
} catch {
    Write-Host "No running containers to stop" -ForegroundColor Yellow
}

Write-Host "🗑️  Removing all containers..." -ForegroundColor Blue
try {
    $allContainers = docker ps -aq
    if ($allContainers) {
        docker rm $allContainers
        Write-Host "Removed containers" -ForegroundColor Green
    } else {
        Write-Host "No containers to remove" -ForegroundColor Yellow
    }
} catch {
    Write-Host "No containers to remove" -ForegroundColor Yellow
}

# Step 2: Remove all images related to the project
Write-Host "🖼️  Removing project images..." -ForegroundColor Blue
try {
    $projectImages = docker images "ghcr.io/nxtleveltech1/dev-xxx" -q
    if ($projectImages) {
        docker rmi $projectImages
        Write-Host "Removed project images" -ForegroundColor Green
    } else {
        Write-Host "No project images to remove" -ForegroundColor Yellow
    }
} catch {
    Write-Host "No project images to remove" -ForegroundColor Yellow
}

# Step 3: Remove all unused images, networks, volumes
Write-Host "🧹 Cleaning up unused Docker resources..." -ForegroundColor Blue
docker system prune -af --volumes

# Step 4: Remove any existing networks
Write-Host "🌐 Removing Docker networks..." -ForegroundColor Blue
docker network prune -f

# Step 5: Pull the latest container image
Write-Host "📦 Pulling latest container image..." -ForegroundColor Blue
docker pull $CONTAINER_IMAGE

# Step 6: Verify the image
Write-Host "🔍 Verifying pulled image..." -ForegroundColor Blue
docker images | Select-String "ghcr.io/nxtleveltech1/dev-xxx"

# Step 7: Start fresh deployment
Write-Host "🚀 Starting fresh deployment..." -ForegroundColor Green
docker-compose -f $COMPOSE_FILE up -d

# Step 8: Wait for container to start
Write-Host "⏳ Waiting for container to start..." -ForegroundColor Blue
Start-Sleep -Seconds 30

# Step 9: Show container status
Write-Host "📋 Container status:" -ForegroundColor Blue
docker-compose -f $COMPOSE_FILE ps

# Step 10: Show logs
Write-Host "📝 Recent logs:" -ForegroundColor Blue
docker-compose -f $COMPOSE_FILE logs --tail=20

# Step 11: Health check
Write-Host "🔍 Performing health check..." -ForegroundColor Blue
try {
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:3000/health" -UseBasicParsing -TimeoutSec 10
    if ($healthResponse.StatusCode -eq 200) {
        Write-Host "✅ Health check passed!" -ForegroundColor Green
    } else {
        throw "Health check failed with status: $($healthResponse.StatusCode)"
    }
} catch {
    Write-Host "❌ Health check failed!" -ForegroundColor Red
    Write-Host "Showing more logs..." -ForegroundColor Yellow
    docker-compose -f $COMPOSE_FILE logs --tail=50
}

# Step 12: Check version
Write-Host "🔍 Checking version endpoint..." -ForegroundColor Blue
try {
    $versionResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/version" -UseBasicParsing -TimeoutSec 10
    Write-Host "Version Response: $($versionResponse.Content)" -ForegroundColor Green
} catch {
    Write-Host "Failed to get version: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "🎉 Fresh deployment completed!" -ForegroundColor Green
Write-Host "🌐 Platform: https://nxtdotx.co.za" -ForegroundColor Green
Write-Host "🔐 Auth API: https://nxtdotx.co.za/api/auth" -ForegroundColor Green
Write-Host "🔍 Health: https://nxtdotx.co.za/health" -ForegroundColor Green
Write-Host "📊 Version: https://nxtdotx.co.za/api/version" -ForegroundColor Green
