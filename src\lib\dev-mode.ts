// LOCAL DEVELOPMENT MODE - BYPASS AUTH FOR RAPID DEVELOPMENT
// This file provides fake auth data for local development

export const isLocalDevelopment = () => {
  return window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
};

export const getFakeUser = () => ({
  id: 'dev-user-123',
  email: '<EMAIL>',
  name: 'Local Dev User',
  emailVerified: true,
  image: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  role: 'admin',
  permissions: JSON.stringify(['read', 'write', 'admin', 'manage_users', 'manage_content']),
  firstName: 'Local',
  lastName: 'Dev',
  displayName: 'Local Dev User',
  department: 'Development',
  phone: null,
  isActive: true
});

export const getFakeSession = () => ({
  user: getFakeUser(),
  session: {
    id: 'dev-session-123',
    userId: 'dev-user-123',
    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    token: 'dev-token-123',
    createdAt: new Date(),
    updatedAt: new Date()
  }
});

console.log('🔧 Dev mode utilities loaded');
