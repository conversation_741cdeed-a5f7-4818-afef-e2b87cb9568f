# Production Deployment Script for NXT Platform (PowerShell)
# Usage: .\deploy.ps1

Write-Host "🚀 Starting NXT Platform Production Deployment..." -ForegroundColor Green

# Configuration
$CONTAINER_IMAGE = "ghcr.io/nxtleveltech1/dev-xxx:latest"
$COMPOSE_FILE = "docker-compose.prod.yml"
$ENV_FILE = ".env.production"

# Check if environment file exists
if (-not (Test-Path $ENV_FILE)) {
    Write-Host "❌ Environment file $ENV_FILE not found!" -ForegroundColor Red
    Write-Host "Please create $ENV_FILE with your production configuration." -ForegroundColor Yellow
    exit 1
}

# Pull latest container image
Write-Host "📦 Pulling latest container image..." -ForegroundColor Blue
docker pull $CONTAINER_IMAGE

# Stop existing containers
Write-Host "🛑 Stopping existing containers..." -ForegroundColor Yellow
docker-compose -f $COMPOSE_FILE down

# Remove old containers and images
Write-Host "🧹 Cleaning up old containers..." -ForegroundColor Yellow
docker system prune -f

# Start new deployment
Write-Host "🚀 Starting new deployment..." -ForegroundColor Green
docker-compose -f $COMPOSE_FILE up -d

# Wait for services to be ready
Write-Host "⏳ Waiting for services to start..." -ForegroundColor Blue
Start-Sleep -Seconds 30

# Health check
Write-Host "🔍 Performing health check..." -ForegroundColor Blue
try {
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:3000/health" -UseBasicParsing
    if ($healthResponse.StatusCode -eq 200) {
        Write-Host "✅ Health check passed!" -ForegroundColor Green
    } else {
        throw "Health check failed with status: $($healthResponse.StatusCode)"
    }
} catch {
    Write-Host "❌ Health check failed!" -ForegroundColor Red
    Write-Host "Checking logs..." -ForegroundColor Yellow
    docker-compose -f $COMPOSE_FILE logs --tail=50
    exit 1
}

# Check version endpoint
Write-Host "🔍 Checking version endpoint..." -ForegroundColor Blue
try {
    $versionResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/version" -UseBasicParsing
    Write-Host "Version: $($versionResponse.Content)" -ForegroundColor Green
} catch {
    Write-Host "Failed to get version: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Show running containers
Write-Host "📋 Running containers:" -ForegroundColor Blue
docker-compose -f $COMPOSE_FILE ps

Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
Write-Host "🌐 Platform available at: https://nxtdotx.co.za" -ForegroundColor Green
Write-Host "🔐 Auth API: https://nxtdotx.co.za/api/auth" -ForegroundColor Green
Write-Host "🔍 Health: https://nxtdotx.co.za/health" -ForegroundColor Green
Write-Host "📊 Version: https://nxtdotx.co.za/api/version" -ForegroundColor Green
