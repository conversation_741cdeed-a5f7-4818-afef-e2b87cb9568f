version: '3.8'

services:
  nxt-platform:
    # LATEST CONTAINER WITH URL CONTEXT MIDDLEWARE FIX
    image: ghcr.io/nxtleveltech1/dev-xxx:latest
    container_name: nxt-platform-final
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      # Database Configuration
      - DATABASE_URL=${DATABASE_URL}
      # Better Auth Configuration
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - BETTER_AUTH_URL=https://nxtdotx.co.za
      # Security Configuration
      - CORS_ORIGIN=https://nxtdotx.co.za
      - TRUSTED_ORIGINS=https://nxtdotx.co.za,https://www.nxtdotx.co.za
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    pull_policy: always
