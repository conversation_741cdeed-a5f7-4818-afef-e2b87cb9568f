# 🚀 Coolify Deployment Guide for NXT Platform

## 📦 Container Information

**Container Image:** `ghcr.io/nxtleveltech1/dev-xxx:latest`
**Container Registry:** GitHub Container Registry (GHCR)
**Platform:** linux/amd64

## 🔧 Coolify Setup Instructions

### 1. Create New Service in Coolify

1. **Login to your Coolify dashboard**
2. **Click "New Resource" → "Service"**
3. **Choose "Docker Image"**
4. **Enter Image Details:**
   - **Image:** `ghcr.io/nxtleveltech1/dev-xxx:latest`
   - **Name:** `nxt-platform`
   - **Port:** `3000`

### 2. Configure Environment Variables

Add these environment variables in Coolify:

```env
NODE_ENV=production
PORT=3000
DATABASE_URL=postgresql://postgres:[password]@[host]:[port]/[database]?sslmode=require
BETTER_AUTH_SECRET=your-32-character-secret-key-here
BETTER_AUTH_URL=https://nxtdotx.co.za
CORS_ORIGIN=https://nxtdotx.co.za
TRUSTED_ORIGINS=https://nxtdotx.co.za,https://www.nxtdotx.co.za
```

### 3. Domain Configuration

1. **Go to "Domains" tab in your service**
2. **Add domain:** `nxtdotx.co.za`
3. **Enable SSL/TLS** (Coolify will handle Let's Encrypt automatically)
4. **Set port mapping:** `3000:3000`

### 4. Health Check Configuration

1. **Go to "Health Checks" tab**
2. **Enable health checks**
3. **Health check path:** `/health`
4. **Health check port:** `3000`
5. **Interval:** `30s`
6. **Timeout:** `10s`
7. **Retries:** `3`

### 5. Resource Limits (Optional)

1. **Go to "Resources" tab**
2. **Set limits:**
   - **Memory:** `1GB`
   - **CPU:** `0.5 cores`

### 6. Deploy

1. **Click "Deploy"**
2. **Monitor logs** for successful startup
3. **Check health endpoint:** `https://nxtdotx.co.za/health`
4. **Verify version:** `https://nxtdotx.co.za/api/version`

## 🔍 Verification Steps

After deployment, verify these endpoints:

1. **Main Site:** https://nxtdotx.co.za
2. **Health Check:** https://nxtdotx.co.za/health
3. **Version Info:** https://nxtdotx.co.za/api/version
4. **Auth API:** https://nxtdotx.co.za/api/auth

Expected version response:
```json
{
  "version": "NATIVE_BETTER_AUTH_v7d96cfc",
  "commit": "7d96cfc",
  "auth_handler": "toNodeHandler(auth)",
  "status": "PRODUCTION_READY"
}
```

## 🔄 Update Deployment

To update to a newer version:

1. **Go to your service in Coolify**
2. **Click "Redeploy"** (this will pull the latest `:latest` tag)
3. **Or update the image tag** to a specific version like `:83ccb82`

## 🐛 Troubleshooting

### If you see 502 errors:
1. Check Coolify logs for the service
2. Verify environment variables are set correctly
3. Ensure the container is healthy
4. Check if the domain is properly configured

### If authentication doesn't work:
1. Verify `BETTER_AUTH_SECRET` is set
2. Check `BETTER_AUTH_URL` matches your domain
3. Ensure `DATABASE_URL` is correct

## 📋 Quick Deployment Checklist

- [ ] Container image: `ghcr.io/nxtleveltech1/dev-xxx:latest`
- [ ] Port mapping: `3000:3000`
- [ ] Domain: `nxtdotx.co.za`
- [ ] SSL enabled
- [ ] Environment variables configured
- [ ] Health check: `/health`
- [ ] Deploy and verify endpoints

## 🎯 Expected Results

After successful deployment:
- ✅ Native Better Auth integration with `toNodeHandler(auth)`
- ✅ No more 502 errors on auth endpoints
- ✅ Proper SSL/TLS with automatic certificates
- ✅ Health monitoring and auto-restart
- ✅ Production-ready configuration
