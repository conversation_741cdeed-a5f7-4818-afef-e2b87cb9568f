# Fix remaining @/auth imports
Write-Host "Fixing remaining @/auth imports..." -ForegroundColor Cyan

$filesToFix = @(
    "src/components/admin/BetaFeatureManagement.tsx",
    "src/components/admin/users/RolesTab.tsx",
    "src/components/auth/AdminPanel.tsx",
    "src/components/auth/LoginForm.tsx",
    "src/components/auth/ManagerDashboard.tsx",
    "src/components/beta/BetaPermissionGuard.tsx",
    "src/components/DebugInfo.tsx",
    "src/components/layout/standard/StandardHeader.tsx",
    "src/components/master-dash/MasterDashHeader.tsx",
    "src/components/master-dash/modules/DataManagement.tsx",
    "src/components/master-dash/modules/EventProduction.tsx",
    "src/components/master-dash/modules/LoyaltyProgram.tsx",
    "src/components/master-dash/modules/RentalsEquipment.tsx",
    "src/components/master-dash/modules/TrainingEducation.tsx",
    "src/components/Navigation.tsx",
    "src/components/ProtectedRoute.tsx",
    "src/components/rag-dashboard/hooks/useDashboardState.ts",
    "src/components/rag-dashboard/PermissionGuard.tsx",
    "src/components/RouteGuard.tsx",
    "src/components/tech-hub/api-management/ApiEndpointList.tsx",
    "src/components/tech-hub/api-management/core/hooks/useApiKeyStorage.ts",
    "src/components/tech-hub/api-management/OpenAIKeyForm.tsx",
    "src/components/tech-hub/api-management/RequestyKeyForm.tsx",
    "src/components/test/AuthTest.tsx",
    "src/components/user-menu.tsx",
    "src/hooks/useModuleAccess.ts",
    "src/hooks/usePersistedState.ts",
    "src/hooks/useRealtimeState.ts",
    "src/hooks/useUserPermissions.ts",
    "src/hooks/useUserPreferences.ts",
    "src/pages/admin/AdminDashboard.tsx",
    "src/pages/admin/DatabaseAdminPage.tsx",
    "src/pages/admin/RolesPermissionsManager.tsx",
    "src/pages/AdminDashboard.tsx",
    "src/pages/Dashboard.tsx",
    "src/pages/data-management/customers/CustomersPage.tsx",
    "src/pages/data-management/DataSourcesPage.tsx",
    "src/pages/MasterDash.tsx",
    "src/pages/techhub/APIProviders.tsx",
    "src/pages/Unauthorized.tsx"
)

$updatedCount = 0
$errorCount = 0

foreach ($file in $filesToFix) {
    if (Test-Path $file) {
        try {
            Write-Host "Fixing $file..." -ForegroundColor Yellow
            
            $content = Get-Content $file -Raw
            
            # Replace @/auth imports with @/providers/BetterAuthProvider
            $content = $content -replace "import \{ useAuth \} from '@/auth';", "import { useBetterAuth } from '@/providers/BetterAuthProvider';"
            $content = $content -replace "import \{ useAuth, ([^}]+) \} from '@/auth';", "import { useBetterAuth } from '@/providers/BetterAuthProvider';`nimport { `$1 } from '@/utils/rbac/permissions';"
            $content = $content -replace "import \{\s*useAuth\s*\} from '@/auth';", "import { useBetterAuth } from '@/providers/BetterAuthProvider';"
            $content = $content -replace "import \{\s*useAuth,\s*([^}]+)\s*\} from '@/auth';", "import { useBetterAuth } from '@/providers/BetterAuthProvider';`nimport { `$1 } from '@/utils/rbac/permissions';"
            
            # Replace useAuth() calls with useBetterAuth()
            $content = $content -replace "const \{ ([^}]+) \} = useAuth\(\);", "const { `$1 } = useBetterAuth();"
            $content = $content -replace "const auth = useAuth\(\);", "const auth = useBetterAuth();"
            $content = $content -replace "useAuth\(\)", "useBetterAuth()"
            
            # Handle loading vs isLoading
            $content = $content -replace "loading:", "isLoading:"
            $content = $content -replace "loading,", "isLoading,"
            $content = $content -replace "loading\s*}", "isLoading }"
            
            Set-Content $file -Value $content -NoNewline
            
            $updatedCount++
            Write-Host "Fixed $file" -ForegroundColor Green
        }
        catch {
            Write-Host "Error fixing $file : $($_.Exception.Message)" -ForegroundColor Red
            $errorCount++
        }
    }
    else {
        Write-Host "File not found: $file" -ForegroundColor Magenta
    }
}

Write-Host ""
Write-Host "Fix Summary:" -ForegroundColor Cyan
Write-Host "Files fixed: $updatedCount" -ForegroundColor Green
Write-Host "Errors: $errorCount" -ForegroundColor Red
Write-Host "Total files processed: $($filesToFix.Count)" -ForegroundColor White

if ($updatedCount -gt 0) {
    Write-Host ""
    Write-Host "All remaining @/auth imports have been fixed!" -ForegroundColor Green
    Write-Host "All files now use useBetterAuth from @/providers/BetterAuthProvider" -ForegroundColor White
}
