#!/usr/bin/env node

/**
 * PHASE 2: BETTER AUTH DATABASE SETUP TEST
 * Tests Better Auth database connection and table creation
 */

import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

console.log('🚀 PHASE 2: BETTER AUTH DATABASE SETUP TEST');
console.log('============================================');

// Validate environment variables
const requiredEnvVars = [
  'DATABASE_URL',
  'BETTER_AUTH_SECRET'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables:', missingVars.join(', '));
  process.exit(1);
}

console.log('✅ All required environment variables are set');

async function testBetterAuthSetup() {
  let client;
  
  try {
    console.log('🔗 Connecting to production database...');
    
    // Create database connection
    client = postgres(process.env.DATABASE_URL, {
      ssl: process.env.NODE_ENV === 'production' ? 'require' : false,
      max: 5,
      idle_timeout: 30,
      connect_timeout: 10,
    });

    const db = drizzle(client);

    // Test basic connection
    const result = await client`SELECT NOW() as current_time, version() as pg_version`;
    console.log('✅ Database connected successfully');
    console.log('📅 Server time:', result[0].current_time);
    console.log('🗄️  PostgreSQL version:', result[0].pg_version.split(' ')[0]);

    console.log('🔧 Initializing Better Auth...');
    
    // Initialize Better Auth - this will create tables automatically
    const auth = betterAuth({
      database: drizzleAdapter(db, {
        provider: "pg",
      }),
      emailAndPassword: {
        enabled: true,
        requireEmailVerification: false,
      },
      session: {
        expiresIn: 60 * 60 * 24 * 7, // 7 days
        updateAge: 60 * 60 * 24, // 24 hours
      },
      secret: process.env.BETTER_AUTH_SECRET,
      baseURL: 'https://nxtdotx.co.za',
      trustedOrigins: [
        'https://nxtdotx.co.za',
        'https://www.nxtdotx.co.za'
      ]
    });

    console.log('✅ Better Auth initialized successfully');

    // Check if Better Auth tables were created
    console.log('🔍 Checking Better Auth tables...');
    
    const tables = await client`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('user', 'session', 'account', 'verification')
      ORDER BY table_name
    `;

    console.log('📋 Better Auth tables found:');
    tables.forEach(table => {
      console.log(`   ✅ ${table.table_name}`);
    });

    if (tables.length === 0) {
      console.log('⚠️  No Better Auth tables found. They will be created on first use.');
    }

    // Test creating a user
    console.log('🧪 Testing user creation...');
    
    try {
      const testUser = await auth.api.signUpEmail({
        body: {
          email: `test_${Date.now()}@example.com`,
          password: 'TestPassword123!',
          name: 'Test User'
        }
      });

      if (testUser) {
        console.log('✅ Test user creation successful');
        
        // Clean up test user
        await client`DELETE FROM "user" WHERE email LIKE '<EMAIL>'`;
        console.log('🧹 Test user cleaned up');
      }
    } catch (error) {
      console.log('⚠️  User creation test failed (this is expected if tables don\'t exist yet):', error.message);
    }

    // Check existing user data
    console.log('📊 Checking existing user data...');
    
    const existingUsers = await client`SELECT COUNT(*) as count FROM auth.users`;
    const existingProfiles = await client`SELECT COUNT(*) as count FROM public.profiles`;
    
    console.log(`📈 Existing auth.users: ${existingUsers[0].count}`);
    console.log(`📈 Existing profiles: ${existingProfiles[0].count}`);

    // Migration plan
    console.log('');
    console.log('📋 MIGRATION PLAN:');
    console.log('1. ✅ Better Auth connection verified');
    console.log('2. ✅ Database tables will be auto-created');
    console.log(`3. 📊 ${existingUsers[0].count} users need migration`);
    console.log(`4. 📊 ${existingProfiles[0].count} profiles need migration`);
    console.log('');
    console.log('🚀 Ready to deploy Better Auth server!');
    console.log('');
    console.log('NEXT STEPS:');
    console.log('1. Deploy the production Better Auth server');
    console.log('2. Better Auth will auto-create tables on first request');
    console.log('3. Migrate existing user data after tables are created');
    console.log('4. Test authentication flow');

  } catch (error) {
    console.error('❌ Better Auth setup test failed:', error);
    console.error('🔍 Error details:', error.message);
    
    if (error.message.includes('connect')) {
      console.log('');
      console.log('🔧 CONNECTION TROUBLESHOOTING:');
      console.log('1. Check DATABASE_URL format');
      console.log('2. Verify database credentials');
      console.log('3. Ensure database is accessible');
      console.log('4. Check SSL configuration');
    }
    
    process.exit(1);
  } finally {
    if (client) {
      await client.end();
    }
  }
}

// Execute test
testBetterAuthSetup();
