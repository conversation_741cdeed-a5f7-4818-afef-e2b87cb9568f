import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useBetterAuth } from '@/providers/BetterAuthProvider';

interface RouteGuardProps {
  children: React.ReactNode;
  fallbackPath?: string;
}

const RouteGuard: React.FC<RouteGuardProps> = ({ 
  children, 
  fallbackPath = '/landing' 
}) => {
  const { user, isLoading } = useBetterAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Only redirect if not loading and no user
    if (!loading && !user) {
      navigate(fallbackPath, { 
        replace: true,
        state: { from: location.pathname }
      });
    }
  }, [user, isLoading, navigate, fallbackPath, location.pathname]);

  // Show loading state or children
  if (loading) {
    return <div className="loading-container">Loading...</div>;
  }

  return user ? <>{children}</> : null;
};

export default RouteGuard;