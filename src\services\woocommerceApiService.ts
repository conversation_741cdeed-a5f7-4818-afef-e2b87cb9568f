/**
 * WooCommerce API Service for Real-Time Data Synchronization
 * Handles batch processing and staging area management
 */

import { supabase } from '@/lib/supabase';

export interface WooCommerceConfig {
  id?: string;
  organization_id: string;
  store_url: string;
  consumer_key: string;
  consumer_secret: string;
  webhook_secret?: string;
  api_version?: string;
  sync_enabled?: boolean;
  batch_size?: number;
  sync_frequency_minutes?: number;
}

export interface WooCommerceCustomer {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  username: string;
  date_created: string;
  date_modified: string;
  billing: {
    first_name: string;
    last_name: string;
    company: string;
    address_1: string;
    address_2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
    email: string;
    phone: string;
  };
  shipping: {
    first_name: string;
    last_name: string;
    company: string;
    address_1: string;
    address_2: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
  is_paying_customer: boolean;
  avatar_url: string;
  meta_data: any[];
}

export interface WooCommerceProduct {
  id: number;
  name: string;
  slug: string;
  permalink: string;
  date_created: string;
  date_modified: string;
  type: string;
  status: string;
  featured: boolean;
  catalog_visibility: string;
  description: string;
  short_description: string;
  sku: string;
  price: string;
  regular_price: string;
  sale_price: string;
  on_sale: boolean;
  purchasable: boolean;
  total_sales: number;
  virtual: boolean;
  downloadable: boolean;
  manage_stock: boolean;
  stock_quantity: number;
  stock_status: string;
  weight: string;
  dimensions: {
    length: string;
    width: string;
    height: string;
  };
  categories: any[];
  tags: any[];
  images: any[];
  attributes: any[];
  meta_data: any[];
}

export interface SyncResult {
  success: boolean;
  records_processed: number;
  records_success: number;
  records_error: number;
  records_skipped: number;
  errors: string[];
  sync_log_id?: string;
}

class WooCommerceApiService {
  private baseUrl: string = '';
  private auth: string = '';
  private config: WooCommerceConfig | null = null;

  /**
   * Initialize the service with WooCommerce configuration
   */
  async initialize(organizationId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('woocommerce_config')
        .select('*')
        .eq('organization_id', organizationId)
        .eq('sync_enabled', true)
        .single();

      if (error || !data) {
        console.error('WooCommerce config not found:', error);
        return false;
      }

      this.config = data;
      this.baseUrl = `${data.store_url}/wp-json/wc/${data.api_version || 'v3'}`;
      this.auth = btoa(`${data.consumer_key}:${data.consumer_secret}`);

      return true;
    } catch (error) {
      console.error('Failed to initialize WooCommerce API:', error);
      return false;
    }
  }

  /**
   * Test API connection
   */
  async testConnection(): Promise<{ success: boolean; message: string }> {
    if (!this.config) {
      return { success: false, message: 'Service not initialized' };
    }

    try {
      const response = await fetch(`${this.baseUrl}/system_status`, {
        headers: {
          'Authorization': `Basic ${this.auth}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        return { success: true, message: 'Connection successful' };
      } else {
        return { success: false, message: `HTTP ${response.status}: ${response.statusText}` };
      }
    } catch (error) {
      return { success: false, message: `Connection failed: ${error}` };
    }
  }

  /**
   * Fetch customers in batches
   */
  async fetchCustomers(page: number = 1, perPage: number = 100): Promise<WooCommerceCustomer[]> {
    if (!this.config) throw new Error('Service not initialized');

    const response = await fetch(`${this.baseUrl}/customers?page=${page}&per_page=${perPage}`, {
      headers: {
        'Authorization': `Basic ${this.auth}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch customers: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Fetch products in batches
   */
  async fetchProducts(page: number = 1, perPage: number = 100): Promise<WooCommerceProduct[]> {
    if (!this.config) throw new Error('Service not initialized');

    const response = await fetch(`${this.baseUrl}/products?page=${page}&per_page=${perPage}`, {
      headers: {
        'Authorization': `Basic ${this.auth}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch products: ${response.statusText}`);
    }

    return await response.json();
  }

  /**
   * Sync customers to staging area
   */
  async syncCustomersToStaging(batchSize: number = 100): Promise<SyncResult> {
    if (!this.config) throw new Error('Service not initialized');

    const syncLogId = await this.createSyncLog('customers', 'batch', batchSize);
    const result: SyncResult = {
      success: true,
      records_processed: 0,
      records_success: 0,
      records_error: 0,
      records_skipped: 0,
      errors: [],
      sync_log_id: syncLogId,
    };

    try {
      let page = 1;
      let hasMore = true;

      while (hasMore) {
        const customers = await this.fetchCustomers(page, batchSize);
        
        if (customers.length === 0) {
          hasMore = false;
          break;
        }

        for (const customer of customers) {
          try {
            await this.insertCustomerToStaging(customer);
            result.records_success++;
          } catch (error) {
            result.records_error++;
            result.errors.push(`Customer ${customer.id}: ${error}`);
          }
          result.records_processed++;
        }

        page++;
        if (customers.length < batchSize) {
          hasMore = false;
        }
      }

      await this.updateSyncLog(syncLogId, 'completed', result);
    } catch (error) {
      result.success = false;
      result.errors.push(`Sync failed: ${error}`);
      await this.updateSyncLog(syncLogId, 'failed', result);
    }

    return result;
  }

  /**
   * Sync products to staging area
   */
  async syncProductsToStaging(batchSize: number = 100): Promise<SyncResult> {
    if (!this.config) throw new Error('Service not initialized');

    const syncLogId = await this.createSyncLog('products', 'batch', batchSize);
    const result: SyncResult = {
      success: true,
      records_processed: 0,
      records_success: 0,
      records_error: 0,
      records_skipped: 0,
      errors: [],
      sync_log_id: syncLogId,
    };

    try {
      let page = 1;
      let hasMore = true;

      while (hasMore) {
        const products = await this.fetchProducts(page, batchSize);
        
        if (products.length === 0) {
          hasMore = false;
          break;
        }

        for (const product of products) {
          try {
            await this.insertProductToStaging(product);
            result.records_success++;
          } catch (error) {
            result.records_error++;
            result.errors.push(`Product ${product.id}: ${error}`);
          }
          result.records_processed++;
        }

        page++;
        if (products.length < batchSize) {
          hasMore = false;
        }
      }

      await this.updateSyncLog(syncLogId, 'completed', result);
    } catch (error) {
      result.success = false;
      result.errors.push(`Sync failed: ${error}`);
      await this.updateSyncLog(syncLogId, 'failed', result);
    }

    return result;
  }

  /**
   * Insert customer into staging table
   */
  private async insertCustomerToStaging(customer: WooCommerceCustomer): Promise<void> {
    const { error } = await supabase
      .from('woocommerce_customers_staging')
      .upsert({
        wc_customer_id: customer.id,
        organization_id: this.config!.organization_id,
        email: customer.email,
        first_name: customer.first_name,
        last_name: customer.last_name,
        username: customer.username,
        date_created: customer.date_created,
        date_modified: customer.date_modified,
        billing_first_name: customer.billing?.first_name,
        billing_last_name: customer.billing?.last_name,
        billing_company: customer.billing?.company,
        billing_address_1: customer.billing?.address_1,
        billing_address_2: customer.billing?.address_2,
        billing_city: customer.billing?.city,
        billing_state: customer.billing?.state,
        billing_postcode: customer.billing?.postcode,
        billing_country: customer.billing?.country,
        billing_email: customer.billing?.email,
        billing_phone: customer.billing?.phone,
        shipping_first_name: customer.shipping?.first_name,
        shipping_last_name: customer.shipping?.last_name,
        shipping_company: customer.shipping?.company,
        shipping_address_1: customer.shipping?.address_1,
        shipping_address_2: customer.shipping?.address_2,
        shipping_city: customer.shipping?.city,
        shipping_state: customer.shipping?.state,
        shipping_postcode: customer.shipping?.postcode,
        shipping_country: customer.shipping?.country,
        is_paying_customer: customer.is_paying_customer,
        avatar_url: customer.avatar_url,
        meta_data: customer.meta_data,
        raw_data: customer,
        sync_status: 'pending',
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'organization_id,wc_customer_id'
      });

    if (error) {
      throw new Error(`Failed to insert customer: ${error.message}`);
    }
  }

  /**
   * Insert product into staging table
   */
  private async insertProductToStaging(product: WooCommerceProduct): Promise<void> {
    const { error } = await supabase
      .from('woocommerce_products_staging')
      .upsert({
        wc_product_id: product.id,
        organization_id: this.config!.organization_id,
        name: product.name,
        slug: product.slug,
        permalink: product.permalink,
        date_created: product.date_created,
        date_modified: product.date_modified,
        type: product.type,
        status: product.status,
        featured: product.featured,
        catalog_visibility: product.catalog_visibility,
        description: product.description,
        short_description: product.short_description,
        sku: product.sku,
        price: parseFloat(product.price || '0'),
        regular_price: parseFloat(product.regular_price || '0'),
        sale_price: parseFloat(product.sale_price || '0'),
        on_sale: product.on_sale,
        purchasable: product.purchasable,
        total_sales: product.total_sales,
        virtual: product.virtual,
        downloadable: product.downloadable,
        manage_stock: product.manage_stock,
        stock_quantity: product.stock_quantity,
        stock_status: product.stock_status,
        weight: product.weight,
        dimensions: product.dimensions,
        categories: product.categories,
        tags: product.tags,
        images: product.images,
        attributes: product.attributes,
        meta_data: product.meta_data,
        raw_data: product,
        sync_status: 'pending',
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'organization_id,wc_product_id'
      });

    if (error) {
      throw new Error(`Failed to insert product: ${error.message}`);
    }
  }

  /**
   * Create sync log entry
   */
  private async createSyncLog(syncType: string, syncMode: string, batchSize: number): Promise<string> {
    const { data, error } = await supabase
      .from('woocommerce_sync_log')
      .insert({
        organization_id: this.config!.organization_id,
        sync_type: syncType,
        sync_mode: syncMode,
        batch_size: batchSize,
        status: 'running',
      })
      .select('id')
      .single();

    if (error) {
      throw new Error(`Failed to create sync log: ${error.message}`);
    }

    return data.id;
  }

  /**
   * Update sync log entry
   */
  private async updateSyncLog(syncLogId: string, status: string, result: SyncResult): Promise<void> {
    const { error } = await supabase
      .from('woocommerce_sync_log')
      .update({
        status,
        records_processed: result.records_processed,
        records_success: result.records_success,
        records_error: result.records_error,
        records_skipped: result.records_skipped,
        completed_at: new Date().toISOString(),
        error_message: result.errors.length > 0 ? result.errors.join('; ') : null,
        sync_details: {
          errors: result.errors,
          batch_size: this.config?.batch_size,
        },
      })
      .eq('id', syncLogId);

    if (error) {
      console.error('Failed to update sync log:', error);
    }
  }

  /**
   * Get staging area statistics
   */
  async getStagingStats(organizationId: string): Promise<{
    customers: { total: number; pending: number; synced: number; error: number };
    products: { total: number; pending: number; synced: number; error: number };
    lastSync: string | null;
  }> {
    const [customersResult, productsResult, lastSyncResult] = await Promise.all([
      supabase
        .from('woocommerce_customers_staging')
        .select('sync_status')
        .eq('organization_id', organizationId),
      supabase
        .from('woocommerce_products_staging')
        .select('sync_status')
        .eq('organization_id', organizationId),
      supabase
        .from('woocommerce_sync_log')
        .select('completed_at')
        .eq('organization_id', organizationId)
        .eq('status', 'completed')
        .order('completed_at', { ascending: false })
        .limit(1)
        .single()
    ]);

    const customerStats = this.calculateStats(customersResult.data || []);
    const productStats = this.calculateStats(productsResult.data || []);

    return {
      customers: customerStats,
      products: productStats,
      lastSync: lastSyncResult.data?.completed_at || null,
    };
  }

  /**
   * Clear staging area data
   */
  async clearStagingArea(organizationId: string, type?: 'customers' | 'products'): Promise<void> {
    if (!type || type === 'customers') {
      const { error } = await supabase
        .from('woocommerce_customers_staging')
        .delete()
        .eq('organization_id', organizationId);
      if (error) throw new Error(`Failed to clear customer staging area: ${error.message}`);
    }
    if (!type || type === 'products') {
      const { error } = await supabase
        .from('woocommerce_products_staging')
        .delete()
        .eq('organization_id', organizationId);
      if (error) throw new Error(`Failed to clear product staging area: ${error.message}`);
    }
  }

  private calculateStats(records: { sync_status: string }[]): {
    total: number;
    pending: number;
    synced: number;
    error: number;
  } {
    return records.reduce((acc, record) => {
      acc.total++;
      switch (record.sync_status) {
        case 'pending':
          acc.pending++;
          break;
        case 'synced':
          acc.synced++;
          break;
        case 'error':
          acc.error++;
          break;
      }
      return acc;
    }, { total: 0, pending: 0, synced: 0, error: 0 });
  }
}

export const woocommerceApiService = new WooCommerceApiService();
