#!/bin/bash

# Complete Docker Cleanup and Fresh Deployment Script
# This will remove ALL containers, images, and networks related to the project

echo "🧹 COMPLETE DOCKER CLEANUP AND FRESH DEPLOYMENT"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONTAINER_IMAGE="ghcr.io/nxtleveltech1/dev-xxx:latest"
COMPOSE_FILE="docker-compose.coolify.yml"

echo -e "${RED}⚠️  WARNING: This will delete ALL Docker containers and images!${NC}"
echo -e "${YELLOW}Press Enter to continue or Ctrl+C to cancel...${NC}"
read

# Step 1: Stop and remove ALL containers
echo -e "${BLUE}🛑 Stopping all running containers...${NC}"
docker stop $(docker ps -aq) 2>/dev/null || echo "No running containers to stop"

echo -e "${BLUE}🗑️  Removing all containers...${NC}"
docker rm $(docker ps -aq) 2>/dev/null || echo "No containers to remove"

# Step 2: Remove all images related to the project
echo -e "${BLUE}🖼️  Removing project images...${NC}"
docker rmi $(docker images "ghcr.io/nxtleveltech1/dev-xxx" -q) 2>/dev/null || echo "No project images to remove"

# Step 3: Remove all unused images, networks, volumes
echo -e "${BLUE}🧹 Cleaning up unused Docker resources...${NC}"
docker system prune -af --volumes

# Step 4: Remove any existing networks
echo -e "${BLUE}🌐 Removing Docker networks...${NC}"
docker network prune -f

# Step 5: Pull the latest container image
echo -e "${GREEN}📦 Pulling latest container image...${NC}"
docker pull $CONTAINER_IMAGE

# Step 6: Verify the image
echo -e "${BLUE}🔍 Verifying pulled image...${NC}"
docker images | grep "ghcr.io/nxtleveltech1/dev-xxx"

# Step 7: Start fresh deployment
echo -e "${GREEN}🚀 Starting fresh deployment...${NC}"
docker-compose -f $COMPOSE_FILE up -d

# Step 8: Wait for container to start
echo -e "${BLUE}⏳ Waiting for container to start...${NC}"
sleep 30

# Step 9: Show container status
echo -e "${BLUE}📋 Container status:${NC}"
docker-compose -f $COMPOSE_FILE ps

# Step 10: Show logs
echo -e "${BLUE}📝 Recent logs:${NC}"
docker-compose -f $COMPOSE_FILE logs --tail=20

# Step 11: Health check
echo -e "${BLUE}🔍 Performing health check...${NC}"
if curl -f http://localhost:3000/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Health check passed!${NC}"
else
    echo -e "${RED}❌ Health check failed!${NC}"
    echo -e "${YELLOW}Showing more logs...${NC}"
    docker-compose -f $COMPOSE_FILE logs --tail=50
fi

# Step 12: Check version
echo -e "${BLUE}🔍 Checking version endpoint...${NC}"
VERSION_RESPONSE=$(curl -s http://localhost:3000/api/version 2>/dev/null || echo "Failed to get version")
echo -e "${GREEN}Version Response: $VERSION_RESPONSE${NC}"

echo -e "${GREEN}🎉 Fresh deployment completed!${NC}"
echo -e "${GREEN}🌐 Platform: https://nxtdotx.co.za${NC}"
echo -e "${GREEN}🔐 Auth API: https://nxtdotx.co.za/api/auth${NC}"
echo -e "${GREEN}🔍 Health: https://nxtdotx.co.za/health${NC}"
echo -e "${GREEN}📊 Version: https://nxtdotx.co.za/api/version${NC}"
