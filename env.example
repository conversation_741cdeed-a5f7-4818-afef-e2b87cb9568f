# NXT-DOT-X Cloud Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# MCP SERVER API KEYS - Add your actual API keys here
# =============================================================================

# Brave Search API (https://brave.com/search/api/)
BRAVE_API_KEY=your-brave-api-key-here

# Tavily Search API (https://tavily.com/)
TAVILY_API_KEY=your-tavily-api-key-here

# FireCrawl API (https://firecrawl.dev/)
FIRECRAWL_API_KEY=your-firecrawl-api-key-here

# Notion API (https://developers.notion.com/)
NOTION_API_KEY=your-notion-api-key-here

# Supabase MCP Configuration
SUPABASE_ACCESS_TOKEN=your-supabase-access-token-here
SUPABASE_PROJECT_ID=your-supabase-project-id-here


# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Supabase Configuration
VITE_SUPABASE_URL=https://utxxvdztmbbjcwdkqxcc.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0eHh2ZHp0bWJiamN3ZGtxeGNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjYxNjMsImV4cCI6MjA2NDc0MjE2M30.O0vgYqezK88HfBU8_BTCljLXTj0ke2JUWWwBFzxtYN8
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0eHh2ZHp0bWJiamN3ZGtxeGNjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTE2NjE2MywiZXhwIjoyMDY0NzQyMTYzfQ.GpmQtjD_WeDvntcacN4heTiLHgPHfxxx7l7ULcW_nsA

# Database Configuration (for server-side operations)
DATABASE_URL=postgresql://postgres.utxxvdztmbbjcwdkqxcc:<EMAIL>:6543/postgres

# Better Auth Configuration
BETTER_AUTH_SECRET=your-better-auth-secret-here-min-32-chars
BETTER_AUTH_URL=https://your-domain.com
VITE_BETTER_AUTH_SECRET=your-better-auth-secret-here-min-32-chars
VITE_BETTER_AUTH_URL=https://your-domain.com

# Production Configuration
PRODUCTION_URL=https://nxtdotx.co.za

# Email Configuration (for Better Auth)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# OAuth Providers (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Cloudflare Turnstile (Optional)
VITE_TURNSTILE_SITE_KEY=your-turnstile-site-key
TURNSTILE_SECRET_KEY=your-turnstile-secret-key

# Server Configuration
PORT=3000
HOST=0.0.0.0
NODE_ENV=production

# Application Configuration
VITE_APP_NAME=NXT-DOT-X
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# API Configuration
VITE_API_BASE_URL=https://utxxvdztmbbjcwdkqxcc.supabase.co/functions/v1
VITE_STORAGE_URL=https://utxxvdztmbbjcwdkqxcc.supabase.co/storage/v1

# Authentication
VITE_AUTH_REDIRECT_URL=https://nxtdotx.co.za/auth/callback
VITE_AUTH_SITE_URL=https://nxtdotx.co.za

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_REALTIME=true
VITE_ENABLE_STORAGE=true

# Development/Debug
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info

# Third-party Integrations
VITE_OPENAI_API_KEY=your-openai-key-here
VITE_STRIPE_PUBLISHABLE_KEY=your-stripe-key-here

# File Upload Limits
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=pdf,doc,docx,xls,xlsx,csv,txt,jpg,jpeg,png,gif 