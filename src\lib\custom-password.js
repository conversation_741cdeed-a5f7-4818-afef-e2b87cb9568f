import argon2 from 'argon2';
import bcrypt from 'bcryptjs';
import { verifyPassword as defaultVerifyPassword, hashPassword } from 'better-auth/crypto';

export async function verifyPassword({ hash, password }) {
  if (typeof hash === 'string') {
    if (hash.startsWith('$argon2')) {
      try {
        return await argon2.verify(hash, password);
      } catch (err) {
        console.error('Argon2 verification failed:', err);
        return false;
      }
    }

    if (hash.startsWith('$2a$') || hash.startsWith('$2b$') || hash.startsWith('$2y$')) {
      try {
        return await bcrypt.compare(password, hash);
      } catch (err) {
        console.error('Bcrypt verification failed:', err);
        return false;
      }
    }
  }

  return defaultVerifyPassword({ hash, password });
}

export { hashPassword };
