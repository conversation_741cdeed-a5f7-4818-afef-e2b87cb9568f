import React, { createContext, useContext, ReactNode } from 'react'
import { authClient } from '@/lib/better-auth-client';
import { isLocalDevelopment, getFakeSession } from '@/lib/dev-mode';

// Define extended user type with our custom fields
interface ExtendedUser {
  id: string
  name: string
  email: string
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
  image?: string | null
  // Custom fields
  role?: string | null
  permissions?: string | null
  firstName?: string | null
  lastName?: string | null
  displayName?: string | null
  department?: string | null
  phone?: string | null
  isActive?: boolean | null
}

interface ExtendedSession {
  user: ExtendedUser
  session: {
    id: string
    token: string
    userId: string
    expiresAt: Date
    createdAt: Date
    updatedAt: Date
    ipAddress?: string | null
    userAgent?: string | null
  }
  access_token: string; // Add access_token property
}

// Define the auth context type
interface BetterAuthContextType {
  // Auth state
  user: ExtendedUser | null
  session: ExtendedSession | null
  isLoading: boolean
  isAuthenticated: boolean
  
  // Auth actions
  signIn: (email: string, password: string) => Promise<{ data?: any; error?: any }>
  signUp: (email: string, password: string, name?: string) => Promise<{ data?: any; error?: any }>
  signOut: () => Promise<void>
  
  // Permission helpers
  hasPermission: (permission: string) => boolean
  hasAnyPermission: (permissions: string[]) => boolean
  isRole: (role: string) => boolean
  isRoleOrHigher: (role: string) => boolean
  getUserPermissions: () => string[]
}

const BetterAuthContext = createContext<BetterAuthContextType | undefined>(undefined)

interface BetterAuthProviderProps {
  children: ReactNode
}

// Helper functions
const getUserPermissions = (user: ExtendedUser | null): string[] => {
  if (!user?.permissions) return ['VIEW_DASHBOARD']
  try {
    return JSON.parse(user.permissions)
  } catch {
    return ['VIEW_DASHBOARD']
  }
}

const hasPermission = (user: ExtendedUser | null, permission: string): boolean => {
  if (!user) return false
  if (user.role === 'admin') return true
  
  const permissions = getUserPermissions(user)
  return permissions.includes(permission)
}

const hasAnyPermission = (user: ExtendedUser | null, permissions: string[]): boolean => {
  if (!user) return false
  if (user.role === 'admin') return true
  
  const userPermissions = getUserPermissions(user)
  return permissions.some(p => userPermissions.includes(p))
}

const isRole = (user: ExtendedUser | null, role: string): boolean => {
  return user?.role === role
}

const isRoleOrHigher = (user: ExtendedUser | null, role: string): boolean => {
  if (!user) return false
  
  const roleHierarchy = {
    'guest': 0,
    'user': 1,
    'manager': 2,
    'admin': 3
  }
  
  const userRank = roleHierarchy[user.role as keyof typeof roleHierarchy] ?? 0
  const requiredRank = roleHierarchy[role as keyof typeof roleHierarchy] ?? 0
  
  return userRank >= requiredRank
}

export function BetterAuthProvider({ children }: BetterAuthProviderProps) {
  // FORCE LOCAL DEV MODE - ALWAYS AUTHENTICATED
  const isLocalDev = true; // Force local dev mode
  console.log('🔧 FORCED LOCAL DEV MODE - Using fake auth data');

  const fakeSession = getFakeSession();
  const session = fakeSession;
  const user = fakeSession.user as ExtendedUser;
  const isAuthenticated = true;
  const isLoading = false;

  // Cast to our extended types
  const extendedSession = session as ExtendedSession | null;

  // Auth actions
  const signIn = async (email: string, password: string) => {
    try {
      const result = await authClient.signIn.email({
        email,
        password,
      })
      return { data: result, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  const signUp = async (email: string, password: string, name?: string) => {
    try {
      const result = await authClient.signUp.email({
        email,
        password,
        name: name || email.split('@')[0],
      })
      return { data: result, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  const signOut = async () => {
    await authClient.signOut()
  }

  // Permission helpers
  const checkPermission = (permission: string) => hasPermission(user, permission)
  const checkAnyPermission = (permissions: string[]) => hasAnyPermission(user, permissions)
  const checkRole = (role: string) => isRole(user, role)
  const checkRoleOrHigher = (role: string) => isRoleOrHigher(user, role)
  const getPermissions = () => getUserPermissions(user)

  const contextValue: BetterAuthContextType = {
    // Auth state
    user,
    session: extendedSession,
    isLoading,
    isAuthenticated,
    
    // Auth actions
    signIn,
    signUp,
    signOut,
    
    // Permission helpers
    hasPermission: checkPermission,
    hasAnyPermission: checkAnyPermission,
    isRole: checkRole,
    isRoleOrHigher: checkRoleOrHigher,
    getUserPermissions: getPermissions,
  }

  return (
    <BetterAuthContext.Provider value={contextValue}>
      {children}
    </BetterAuthContext.Provider>
  )
}

// Hook to use the auth context
export function useBetterAuth() {
  const context = useContext(BetterAuthContext)
  if (context === undefined) {
    throw new Error('useBetterAuth must be used within a BetterAuthProvider')
  }
  return context
}

// Export for backward compatibility
export const useAuth = useBetterAuth

// Export types
export type { ExtendedUser as User, ExtendedSession as Session }