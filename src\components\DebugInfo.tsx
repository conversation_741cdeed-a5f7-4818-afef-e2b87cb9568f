import React from 'react';
import { useBetterAuth } from '@/providers/BetterAuthProvider';
import { useLocation } from 'react-router-dom';

const DebugInfo: React.FC = () => {
  const { user, session, isLoading, isAuthenticated } = useBetterAuth();
  const location = useLocation();

  return (
    <div className="fixed top-0 right-0 bg-black text-white p-2 text-xs z-50 max-w-xs">
      <div>Path: {location.pathname}</div>
      <div>isLoading: {loading.toString()}</div>
      <div>IsAuth: {isAuthenticated.toString()}</div>
      <div>User: {user ? user.email : 'null'}</div>
      <div>Session: {session ? 'exists' : 'null'}</div>
      <div>User Role: {user?.role || 'none'}</div>
    </div>
  );
};

export default DebugInfo;
