
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useBetterAuth } from '@/providers/BetterAuthProvider'; // Updated to use unified auth system
import LoginForm from '@/components/auth/LoginForm';
import { LandingBackground } from '@/components/auth/LandingBackground';

const Landing = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isLoading, signIn } = useBetterAuth();

  // Check if user is already logged in
  useEffect(() => {
    if (isLoading) return; // Wait for auth to complete
    
    if (isAuthenticated) {
      // Check if there's a return URL in the query parameters
      const params = new URLSearchParams(globalThis.location.search);
      const returnPath = params.get('returnUrl');
      
      if (returnPath) {
        // Remove the returnUrl query parameter
        params.delete('returnUrl');
        const newUrl = `${globalThis.location.pathname}${params.toString() ? '?' + params.toString() : ''}`;
        globalThis.history.replaceState({}, document.title, newUrl);
        
        navigate(returnPath);
      } else {
        navigate('/');
      }
    }
  }, [navigate, isAuthenticated, isLoading]);

  const handleLogin = async (email: string, password: string) => {
    try {
      const result = await signIn(email, password);
      if (result.error) {
        console.error('Login failed:', result.error);
      } else {
        // Navigation will be handled by the useEffect above
        console.log('Login successful');
      }
    } catch (error) {
      console.error('Login error:', error);
    }
  };
  
  return (
    <LandingBackground backgroundImageUrl="/lovable-uploads/d4803c2c-c894-4a2d-ab2c-1e8d71bea99f.png">
      <LoginForm onLogin={handleLogin} isLoading={isLoading} />
    </LandingBackground>
  );
};

export default Landing;
