{"include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["src/**/*.d.ts", "backups", "ts-fix", "node_modules", "src/server/**/*", "src/tests/**/*"], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@root/*": ["../src/*"]}, "noImplicitAny": false, "noUnusedParameters": false, "skipLibCheck": true, "allowJs": true, "noUnusedLocals": false, "strictNullChecks": false, "forceConsistentCasingInFileNames": true, "strict": true, "lib": ["dom", "dom.iterable", "esnext"], "noEmit": true, "incremental": true, "module": "esnext", "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-jsx"}}