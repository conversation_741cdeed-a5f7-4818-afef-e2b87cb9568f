# Coolify Environment Configuration for NXT Platform
# Copy these variables to your Coolify environment settings

NODE_ENV=production
PORT=3000

# Database Configuration (Supabase)
DATABASE_URL=postgresql://postgres:[password]@[host]:[port]/[database]?sslmode=require

# Better Auth Configuration
BETTER_AUTH_SECRET=your-32-character-secret-key-here
BETTER_AUTH_URL=https://nxtdotx.co.za

# Security Configuration
CORS_ORIGIN=https://nxtdotx.co.za
TRUSTED_ORIGINS=https://nxtdotx.co.za,https://www.nxtdotx.co.za
