import React from 'react';
import { Routes, Route } from 'react-router-dom';

// WooCommerce components
const WooCommerceDashboard = React.lazy(() => import('../pages/WooCommerceDashboard'));

/**
 * WooCommerce Routes
 * Handles all WooCommerce integration routes
 */
const WooCommerceRoutes: React.FC = () => {
  return (
    <Routes>
      <Route index element={<WooCommerceDashboard />} />
      <Route path="dashboard" element={<WooCommerceDashboard />} />
      <Route path="products" element={<WooCommerceDashboard />} />
      <Route path="orders" element={<WooCommerceDashboard />} />
      <Route path="customers" element={<WooCommerceDashboard />} />
      <Route path="settings" element={<WooCommerceDashboard />} />
    </Routes>
  );
};

export default WooCommerceRoutes;
